import CryptoPageWrapper from "./CryptoPageWrapper";
import { fetchCoingeckoCoinsData } from "@/app/services/http/coingeckoService";
import { fetchCoinmarketcapCoinsData } from "@/app/services/http/coinmarketcapService";

export default async function Cripto(): Promise<React.ReactNode> {
  const [_coingeckoCoinsData, _coinmarketcapCoinsData] = await Promise.all([
    fetchCoingeckoCoinsData(),
    fetchCoinmarketcapCoinsData(),
  ]);
  return (
    <>
      <p className="m-3 text-sm text-zinc-500">
        Le criptovalute sono una forma di moneta digitale che si basa su un{" "}
        <span className="text-sm font-bold text-zinc-300">
          sistema decentralizzato
        </span>
        , senza l’intervento di banche centrali o governi.
        <br /> <br />
        La loro esistenza è resa possibile dalla tecnologia{" "}
        <span className="text-sm font-bold text-zinc-300">blockchain</span>, un
        registro distribuito che garantisce la sicurezza e l’immutabilità delle
        transazioni.
        {/* <br />
        <br />
        L’uso delle criptovalute e della blockchain ha trovato applicazioni in
        diversi settori: */}
      </p>
      {/* <ul className="my-3 mx-8 text-sm text-zinc-500 list-disc">
        <li className="text-sm">Finanza decentralizzata (DeFi)</li>
        <li className="text-sm">Smart Contract e app decentralizzate (DApp)</li>
        <li className="text-sm">Tokenizzazione di asset e NFT</li>
        <li className="text-sm">Metaverso e gaming</li>
      </ul> */}

      <CryptoList
        coingeckoCoinsData={_coingeckoCoinsData}
        coinmarketcapCoinsData={_coinmarketcapCoinsData}
      ></CryptoList>
    </>
  );
}
