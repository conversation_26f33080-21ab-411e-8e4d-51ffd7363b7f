import { ICriptovalutaNews } from "@/app/interfaces/criptovalutaNews";
import { parseStringPromise } from "xml2js";

const CRIPTOVALUTA_RSS_URL = "https://www.criptovaluta.it/feed";

export async function fetchCriptovalutaNews(): Promise<ICriptovalutaNews[]> {
  try {
    console.log("Effettuando una nuova chiamata a Criptovaluta News...");
    const response = await fetch(CRIPTOVALUTA_RSS_URL, {
      next: { revalidate: 60 * 2 },
    }); // Aggiorna la cache ogni 2 minuti

    const xml = await response.text();
    const result = await parseStringPromise(xml);

    // Mappatura dati RSS
    const items = result.rss.channel[0].item
      .filter((item: any) => !item.category.includes("Analisi On Chain"))
      .map((item: any) => ({
        title: item.title,
        link: item.link,
        pubDate: item.pubDate,
        category: item.category,
        description: item.description,
        content: item.content,
      }));

    if (!response.ok) {
      throw new Error("Errore durante il fetch da Criptovaluta News");
    }

    return items as ICriptovalutaNews[];
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione Criptovaluta News",
      error
    );
    throw new Error("Errore durante il recupero dei dati Criptovaluta News");
  }
}
