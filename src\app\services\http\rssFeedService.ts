import { INews, INewsResponse } from "@/app/interfaces/db/news";
import {
  getLatestGeminiSummary,
  saveGeminiSummary,
} from "../db/supabaseService";
import { summarizeArticles } from "./geminiService";

const WATCHERGURU_RSS_FEED = process.env.WATCHERGURU_RSS_FEED!;
const GEMINISUMMARY_CACHE_DURATION = 1000 * 60 * 60; // 1 ora

/**
 * Recupera i tweet da una lista di account e li inserisce (o aggiorna)
 * nella tabella Supabase "tweets". Utilizza fetchTweetsForMultipleUsers per
 * effettuare una singola chiamata combinata.
 *
 * @param start_time - (Opzionale) Timestamp ISO8601 di inizio
 * @param end_time - (Opzionale) Timestamp ISO8601 di fine
 * @returns Un array dei tweet inseriti/aggiornati
 */
export async function fetchAndStoreRssFeed(): Promise<{
  sentiment: string;
  summary: string;
  coin_tickers: string[];
  created_at: string;
}> {
  try {
    console.log("🔄 Controllo cache Supabase per il riassunto...");

    if (await isGeminiCacheValid()) {
      const cachedSummary = await getLatestGeminiSummary();
      console.log(
        "✅ Servendo Gemini Summary dalla cache Supabase",
        cachedSummary
      );
      return cachedSummary!;
    }

    console.log(
      "🆕 Cache scaduta o non trovata, generazione nuovo riassunto..."
    );

    // **Recupera le ultime notizie**
    const response = await fetch(WATCHERGURU_RSS_FEED, {
      next: { revalidate: 60 * 60 }, // Aggiorna dati ogni 1 ora
    });

    const responseData = await response.json();

    const news: INews[] = responseData.items
      .map(
        (item: INewsResponse) =>
          ({
            _id: item.id,
            username: item.authors[0].name,
            title: item.title,
            full_text: item.content_text,
            created_at: item.date_published,
            url: item.url,
          } as INews)
      )
      .reverse();

    // **Genera il nuovo riassunto con Gemini**
    const gptSummary = await summarizeArticles(news.map((n) => n.title));

    // **Salva il nuovo riassunto su Supabase**
    await saveGeminiSummary(
      gptSummary.summary,
      gptSummary.sentiment,
      gptSummary.coin_tickers
    );

    console.log("✅ Nuovo riassunto salvato in Supabase:", gptSummary);

    return gptSummary;
  } catch (error) {
    console.error(`❌ Errore durante il recupero dei dati RSS: ${error}`);
    throw new Error(`Errore durante il recupero dei dati RSS: ${error}`);
  }
}

/**
 * Controlla se la cache è ancora valida (meno di 1 ora di differenza)
 */
export async function isGeminiCacheValid() {
  const latestSummary = await getLatestGeminiSummary();

  if (!latestSummary) return false;

  const currentTime = Date.now();
  const lastFetchTime = new Date(latestSummary.created_at).getTime();
  const timeDifference = currentTime - lastFetchTime;

  return timeDifference < GEMINISUMMARY_CACHE_DURATION;
}
