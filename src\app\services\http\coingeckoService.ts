import {
  ICoingeckoCategoriesData,
  ICoingeckoCoinsData,
  ICoingeckoCompanyHoldersData,
} from "@/app/interfaces/coingecko";

const COINGECKO_COINS_URL =
  "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=250&page=1&sparkline=false&precision=full";
const COINGECKO_CATEGORIES_URL =
  "https://api.coingecko.com/api/v3/coins/categories?order=market_cap_desc";
const COINGECKO_COMPANY_HOLDERS_ETH_URL =
  "https://api.coingecko.com/api/v3/companies/public_treasury/ethereum";

// Fetch coins
export async function fetchCoingeckoCoinsData(): Promise<
  ICoingeckoCoinsData[]
> {
  try {
    console.log("Effettuando una nuova chiamata a CoinGecko - Coins...");

    const response = await fetch(COINGECKO_COINS_URL, {
      next: { revalidate: 30 }, // Aggiorna la cache ogni 30 secondi
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });

    if (!response.ok) {
      throw new Error(`Errore HTTP: ${response.status}`);
    }

    const data: ICoingeckoCoinsData[] = await response.json();
    // console.log("Chiamata a CoinGecko - Coins andata a buon fine!");

    return data;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione CoinGecko (Coins):",
      error
    );
    throw new Error("Errore durante il recupero dei dati CoinGecko (Coins)");
  }
}

// Fetch categories
export async function fetchCoingeckoCategoriesData(): Promise<
  ICoingeckoCategoriesData[]
> {
  try {
    const response = await fetch(COINGECKO_CATEGORIES_URL, {
      next: { revalidate: 60 * 5 }, // Aggiorna la cache ogni 5 minuti
    });

    if (!response.ok) {
      throw new Error("Errore durante il fetch da CoinGecko (Categorie)");
    }

    const data: ICoingeckoCategoriesData[] = await response.json();
    // console.log("Chiamata a CoinGecko - Categorie andata a buon fine!");

    return data;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione CoinGecko (Categorie):",
      error
    );
    throw new Error(
      "Errore durante il recupero dei dati CoinGecko (Categorie)"
    );
  }
}

// Fetch company ETH
export async function fetchCoingeckoCompanyHoldersData(): Promise<ICoingeckoCompanyHoldersData> {
  try {
    const response = await fetch(COINGECKO_COMPANY_HOLDERS_ETH_URL, {
      next: { revalidate: 60 * 30 }, // Aggiorna la cache ogni 30 minuti
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error("Errore durante il fetch da Coingecko (Company)");
    }

    return data;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione CoinGecko (Company):",
      error
    );
    throw new Error("Errore durante il recupero dei dati CoinGecko (Company)");
  }
}
