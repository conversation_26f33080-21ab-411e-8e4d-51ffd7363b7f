"use client";

import { Tab, Tabs } from "@heroui/react";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

enum tabType {
  dashboard = "dashboard",
  notizie = "notizie",
  cripto = "cripto",
  categorie = "categorie",
  blockchain = "blockchain",
  token = "token",
  volumi = "volumi",
}

export default function TabsMenu() {
  const router = useRouter();
  const pathname = usePathname();

  const [selected, setSelected] = useState<tabType>(() => {
    // Remove "/" from pathname
    return pathname.slice(1) as tabType;
  });

  const onSelectChange = (key: tabType) => {
    if (selected === null) {
      return;
    }

    console.log("change page", key);
    router.push(key);
    setSelected(key);
  };

  // Nascondi il menu se siamo su /login
  if (pathname === "/login") {
    return null;
  }

  return (
    <div className="bg-[#0a0a0a]">
      <div className="flex w-100 flex-col relative">
        <Tabs
          aria-label="Options"
          size="md"
          fullWidth
          variant="underlined"
          defaultSelectedKey={selected}
          color="primary"
          selectedKey={selected}
          onSelectionChange={(key) => onSelectChange(key as tabType)}
          classNames={{
            base: "w-full relative w-full pt-0 mt-[-6px] after:absolute after:bottom-[4.5px] after:left-0 after:w-full after:h-[1px] after:bg-zinc-800 z-10 mb-2",
            tabList: "",
            tab: "z-20 w-[fit-content] text-start",
            cursor: "pointer",
          }}
        >
          <Tab
            key={tabType.dashboard}
            title="Dashboard"
            className={`px-3 ${
              selected === tabType.dashboard ? "font-bold " : ""
            }`}
          ></Tab>
          <Tab
            key={tabType.notizie}
            title="Notizie"
            className={`px-2 ${
              selected === tabType.notizie ? "font-bold" : ""
            }`}
          ></Tab>
          <Tab
            key={tabType.cripto}
            title="Cripto"
            className={`px-2 ${selected === tabType.cripto ? "font-bold" : ""}`}
          ></Tab>
          <Tab
            key={tabType.blockchain}
            title="Blockchain"
            className={`px-2 ${
              selected === tabType.blockchain ? "font-bold" : ""
            }`}
          ></Tab>
          <Tab
            key={tabType.token}
            title="Token"
            className={`px-2 ${selected === tabType.token ? "font-bold" : ""}`}
          ></Tab>
          <Tab
            key={tabType.categorie}
            title="Categorie"
            className={`px-2 ${
              selected === tabType.categorie ? "font-bold" : ""
            }`}
          ></Tab>

          <Tab
            key={tabType.volumi}
            title="Volumi"
            className={`px-2 ${selected === tabType.volumi ? "font-bold" : ""}`}
          ></Tab>
        </Tabs>
      </div>
    </div>
  );
}
