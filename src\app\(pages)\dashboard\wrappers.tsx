import { BtcGlobalWidget } from "@/app/components/widgets/BtcGlobalWidget";
import { EtfWidget } from "@/app/components/widgets/EtfWidget";
import { EthGlobalWidget } from "@/app/components/widgets/EthGlobalWidget";
import { fetchBtcGlobal } from "@/app/services/http/bitboTreasuriesService";
import {
  fetchCoingeckoCoinsData,
  fetchCoingeckoCompanyHoldersData,
} from "@/app/services/http/coingeckoService";
import { fetchEtfHistory } from "@/app/services/http/googleSheetService";

export async function EtfWidgetWrapper(): Promise<React.ReactNode> {
  const _etfHistory = await fetchEtfHistory();

  return (
    <>
      <EtfWidget selectedName="btc" etfHistory={_etfHistory}></EtfWidget>
      <EtfWidget selectedName="eth" etfHistory={_etfHistory}></EtfWidget>
    </>
  );
}

export async function GlobalWidgetWrapper(): Promise<React.ReactNode> {
  const [
    _coingeckoCoinsData,
    _coingeckoCompanyHoldersData,
    _etfHistory,
    _btcGlobal,
  ] = await Promise.all([
    fetchCoingeckoCoinsData(),
    fetchCoingeckoCompanyHoldersData(),
    fetchEtfHistory(),
    fetchBtcGlobal(),
  ]);

  return (
    <>
      <div className="mx-3 my-8 grid grid-cols-1 gap-4">
        <BtcGlobalWidget btcGlobal={_btcGlobal}></BtcGlobalWidget>
      </div>

      <div className="mx-3 my-8 grid grid-cols-1 gap-4">
        <EthGlobalWidget
          etfHistory={_etfHistory}
          coingeckoCompanyHoldersData={_coingeckoCompanyHoldersData}
          coingeckoCoinsData={_coingeckoCoinsData}
        ></EthGlobalWidget>
      </div>
    </>
  );
}
