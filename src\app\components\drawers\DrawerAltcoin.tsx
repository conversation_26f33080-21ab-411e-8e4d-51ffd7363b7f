import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DrawerHeader,
} from "@heroui/react";
import { memo, useEffect, useRef } from "react";

export default function DrawerAltcoin({
  isDrawerOpen,
  onClose,
}: {
  isDrawerOpen: boolean;
  onClose: () => void;
}) {
  return (
    <>
      <Drawer
        isOpen={isDrawerOpen}
        onOpenChange={onClose}
        placement="bottom"
        className="h-[550px]"
      >
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader className="flex justify-center">
                <div className="w-100 flex flex-col items-center">
                  <div className="text-md"> Capitalizzazione Altcoin</div>
                  <div className="text-xs text-zinc-500">
                    (Escludendo BTC, ETH, USDT, USDC)
                  </div>
                </div>
              </DrawerHeader>
              <DrawerBody className="h-[500px] p-2">
                <TradingViewChart></TradingViewChart>
              </DrawerBody>
              <DrawerFooter>
                {/* <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button> */}
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </>
  );
}

const TradingViewChart = memo(() => {
  const container = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
        {
          "autosize": true,
          "symbol": "TOTAL3-USDT-USDC",
          "interval": "D",
          "timezone": "Europe/Rome",
          "theme": "dark",
          "style": "2",
          "locale": "it",
          "hide_legend": true,
          "hide_top_toolbar": true,
          "allow_symbol_change": false,
          "save_image": false,
          "calendar": false,
          "hide_volume": true,
          "support_host": "https://www.tradingview.com"
        }`;
    container!.current!.appendChild(script);
  }, []);

  return (
    <div
      className="tradingview-widget-container"
      ref={container}
      style={{ height: "100%", width: "100%" }}
    >
      <div
        className="tradingview-widget-container__widget"
        style={{ height: "calc(100% - 12px)", width: "100%" }}
      ></div>
      <div className="tradingview-widget-copyright">
        <a rel="noopener nofollow" target="_blank">
          {/* <span className="blue-text">
            Segui tutti i mercati su TradingView
          </span> */}
        </a>
      </div>
    </div>
  );
});
