"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>vbar<PERSON>ontent,
} from "@heroui/react";
import { signOut } from "next-auth/react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

export default function Header() {
  const pathname = usePathname();
  const router = useRouter();
  // Nascondi il menu se siamo su /login
  if (pathname === "/login") {
    return null;
  }

  return (
    <Navbar
      isBordered
      maxWidth="full"
      position="static"
      isBlurred={false}
      classNames={{
        wrapper: "px-3",
        base: "border-none",
      }}
    >
      <NavbarBrand
        className="w-[30px] cursor-pointer"
        onClick={() => router.push("/")}
      >
        <Image
          src="/logo.png"
          alt="logo"
          width={25}
          height={25}
          className="rounded-2xl "
        />
        <span className="ml-2 text-xl text-white tracking-wider font-semibold">
          Elite Cripto
        </span>
      </NavbarBrand>

      <NavbarContent justify="end">
        <Logout></Logout>
      </NavbarContent>
    </Navbar>
  );
}

const Logout = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <LogoutModal
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      ></LogoutModal>
      <button aria-label="logout" onClick={() => setIsModalOpen(true)}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          className="w-[15px] h-[15px]"
        >
          <path
            fill="gray"
            d="M502.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-128-128c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L402.7 224 192 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l210.7 0-73.4 73.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l128-128zM160 96c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 32C43 32 0 75 0 128L0 384c0 53 43 96 96 96l64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l64 0z"
          />
        </svg>
      </button>
    </>
  );
};

const LogoutModal = ({
  isModalOpen,
  onClose,
}: {
  isModalOpen: boolean;
  onClose: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const handleLogout = () => {
    setIsLoading(true);
    signOut();
  };
  return (
    <>
      <Modal
        isOpen={isModalOpen}
        onClose={onClose}
        placement="center"
        className="mx-6"
        classNames={{
          backdrop: "bg-black/75",
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">Logout</ModalHeader>
              <ModalBody>
                <p>Sei sicuro di voler effettuare il logout?</p>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="primary"
                  variant="light"
                  onPress={onClose}
                  isDisabled={isLoading}
                >
                  Annulla
                </Button>
                <Button
                  color="danger"
                  onPress={handleLogout}
                  isDisabled={isLoading}
                  isLoading={isLoading}
                >
                  {isLoading ? null : "Logout"}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
