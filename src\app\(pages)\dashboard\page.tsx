import { AltcoinSeasonIndexWidget } from "@/app/components/widgets/AltcoinSeasonIndexWidget";
import { DominanceWidget } from "@/app/components/widgets/DominanceWidget";
import { Top3Coins } from "@/app/components/widgets/Top3Coins";
import { TopMarketCapWidget } from "@/app/components/widgets/TopMarketCapWidget";
import { ICriptovalutaNews } from "@/app/interfaces/criptovalutaNews";
import { fetchBtcGlobal } from "@/app/services/http/bitboTreasuriesService";
import {
  fetchCoingeckoCoinsData,
  fetchCoingeckoCompanyHoldersData,
} from "@/app/services/http/coingeckoService";
import { fetchCoinmarketcapCoinsData } from "@/app/services/http/coinmarketcapService";
import { fetchCriptovalutaNews } from "@/app/services/http/criptovalutaNewsService";
import { fetchAndStoreRssFeed } from "@/app/services/http/rssFeedService";
import { Suspense } from "react";
import { EtfLoader } from "./etf-loader";

import React from "react";
import { GlobalLoader } from "./global-loader";
import { MarketOverview } from "./marketOverview";

export default async function Dashboard() {
  console.log("🔄 Generazione della Dashboard...");

  const summary = await fetchAndStoreRssFeed();

  console.log("🔄 News", summary);

  const [
    _coingeckoCoinsData,
    _coinmarketcapCoinsData,
    _criptovalutaNews,
    _btcGlobal,
    _coingeckoCompanyHoldersData,
  ] = await Promise.all([
    fetchCoingeckoCoinsData(),
    fetchCoinmarketcapCoinsData(),
    fetchCriptovalutaNews(),
    fetchBtcGlobal(),
    fetchCoingeckoCompanyHoldersData(),
  ]);

  const filteredCoinmarketcapData = _coinmarketcapCoinsData
    .slice(0)
    .filter(
      (item) =>
        !item.tags.includes("stablecoin") && !item.slug.includes("classic")
    );

  const filteredCriptovalutaNews = _criptovalutaNews!.filter(
    (item: ICriptovalutaNews) => item.category.includes("News")
  );

  // const summaryHtml = { __html: summary };

  const EtfWidgetWrapper = React.lazy(() =>
    import("./wrappers").then((mod) => ({ default: mod.EtfWidgetWrapper }))
  );

  const GlobalWidgetWrapper = React.lazy(() =>
    import("./wrappers").then((mod) => ({ default: mod.GlobalWidgetWrapper }))
  );

  return (
    <div className="mb-8">
      {/* Widgets */}

      <div className="p-3">
        <MarketOverview
          lastNewsReport={summary}
          coingeckoCoinsData={_coingeckoCoinsData}
        ></MarketOverview>
      </div>
      {/* <p className="p-3" dangerouslySetInnerHTML={summaryHtml}>
          {summary.summary}
        </p> */}
      {/* <NewsWidget criptovalutaNews={filteredCriptovalutaNews}></NewsWidget> */}

      <div className="mx-3 mt-4 mb-8 grid grid-cols-2 gap-4">
        <DominanceWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        ></DominanceWidget>
        <AltcoinSeasonIndexWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        ></AltcoinSeasonIndexWidget>
      </div>

      <div className="mx-3 my-8">
        <TopMarketCapWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
          coingeckoCoinsData={_coingeckoCoinsData}
        ></TopMarketCapWidget>
      </div>

      <div className="mx-3 my-8 grid grid-cols-2 gap-4">
        <Top3Coins
          range="7gg"
          coingeckoCoinsData={_coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        ></Top3Coins>
        <Top3Coins
          range="30gg"
          coingeckoCoinsData={_coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        ></Top3Coins>
      </div>

      <div className="mx-3 my-8 grid grid-cols-2 gap-4">
        <Suspense fallback={<EtfLoader />}>
          <EtfWidgetWrapper></EtfWidgetWrapper>
        </Suspense>
      </div>
      {/* <h2 className="mx-4 my-8 text-xl font-bold flex items-center">
          <span className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 448 512"
              className="w-4 mr-2 fill-white"
            >
              <path d="M32 0C49.7 0 64 14.3 64 32l0 16 69-17.2c38.1-9.5 78.3-5.1 113.5 12.5c46.3 23.2 100.8 23.2 147.1 0l9.6-4.8C423.8 28.1 448 43.1 448 66.1l0 36.1-44.7 16.2c-42.8 15.6-90 13.9-131.6-4.6l-16.1-7.2c-20.3-9-41.8-14.7-63.6-16.9l0 32.2c17.4 2.1 34.4 6.7 50.6 13.9l16.1 7.2c49.2 21.9 105 23.8 155.6 5.4L448 136.3l0 62-44.7 16.2c-42.8 15.6-90 13.9-131.6-4.6l-16.1-7.2c-40.2-17.9-85-22.5-128.1-13.3L64 203.1l0 32.7 70.2-15.1c36.4-7.8 74.3-3.9 108.4 11.3l16.1 7.2c49.2 21.9 105 23.8 155.6 5.4L448 232.3l0 62-44.7 16.2c-42.8 15.6-90 13.9-131.6-4.6l-16.1-7.2c-40.2-17.9-85-22.5-128.1-13.3L64 299.1l0 32.7 70.2-15.1c36.4-7.8 74.3-3.9 108.4 11.3l16.1 7.2c49.2 21.9 105 23.8 155.6 5.4L448 328.3l0 33.5c0 13.3-8.3 25.3-20.8 30l-34.7 13c-46.2 17.3-97.6 14.6-141.7-7.4c-37.9-19-81.3-23.7-122.5-13.4L64 400l0 80c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64 0-70.5 0-32.7 0-63.3 0-32.7 0-63.3 0-32.7L0 64 0 32C0 14.3 14.3 0 32 0zm80 96A16 16 0 1 0 80 96a16 16 0 1 0 32 0zm32 0a16 16 0 1 0 0-32 16 16 0 1 0 0 32zm-32 48a16 16 0 1 0 -32 0 16 16 0 1 0 32 0zm32 0a16 16 0 1 0 0-32 16 16 0 1 0 0 32z" />
            </svg>
          </span>
          Adozione globale
        </h2> */}
      <Suspense fallback={<GlobalLoader />}>
        <GlobalWidgetWrapper></GlobalWidgetWrapper>
      </Suspense>
    </div>
  );
}
