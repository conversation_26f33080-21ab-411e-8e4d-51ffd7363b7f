export const timeFromNow = (timestamp: string, longText?: boolean): string => {
  const now = new Date();
  const dateToCompare = new Date(timestamp);
  const timeDifference = now.getTime() - dateToCompare.getTime();
  const seconds = Math.floor(timeDifference / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return days === 1 ? "1 giorno" : `${days} giorni ${longText ? "fa" : ""}`;
  } else if (hours > 0) {
    return hours === 1 ? "1 ora" : `${hours} ore ${longText ? "fa" : ""}`;
  } else if (minutes > 0) {
    return minutes === 1 ? "1 min" : `${minutes} min ${longText ? "fa" : ""}`;
  } else {
    return seconds === 1 ? "1 sec" : `${seconds} sec ${longText ? "fa" : ""}`;
  }
};
