import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import Footer from "./components/shared/Footer";
import Header from "./components/shared/Header";
import TabsMenu from "./components/shared/Tabs";
import "./globals.css";
import { Providers } from "./providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["400", "500", "700"],
});

export const metadata: Metadata = {
  title: "Elite Cripto",
  description: "Dashboard cripto",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${roboto.variable} antialiased`}>
        <Providers>
          <div className="sticky top-0 z-50">
            <Header></Header>
            <TabsMenu></TabsMenu>
          </div>
          <div className="mt-[-5px]">{children}</div>
        </Providers>
        <Footer></Footer>
      </body>
    </html>
  );
}
